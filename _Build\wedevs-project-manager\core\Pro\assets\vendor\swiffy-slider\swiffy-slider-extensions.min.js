/*!
 * swiffy-slider version: "1.6.0",
 * https://github.com/dynamicweb/swiffy-slide
 * Licensed MIT
 * Copyright (c) 2021 Dynamicweb Software A/S
 */
const swiffysliderextensions={version:"1.6.0",draggingtimer:null,init(e=document.body){e.querySelectorAll(".swiffy-slider").forEach(e=>this.initSlider(e))},initSlider(e){e.classList.contains("slider-nav-mousedrag")&&e.addEventListener("mousedown",s=>this.handleMouseDrag(s,e),{passive:!0})},handleMouseDrag(e,s){if(e.srcElement.classList.contains("slider-nav")||e.srcElement.parentElement.classList.contains("slider-indicators"))return;const t=s.querySelector(".slider-container");s.classList.contains("dragging")&&clearTimeout(this.draggingtimer),t.style.cursor="grabbing",s.classList.add("dragging");const i=t.scrollLeft,n=e.clientX,r=t.children[0].offsetWidth+parseInt(window.getComputedStyle(t).columnGap),o=r*(t.children.length-1),l=t.scrollLeft;let d=l;const a=e=>{const s=e.clientX-n,a=i-1.8*s;a>0&&a<=o&&(t.scrollLeft=a,s<0?d=o<=l?l:t.scrollLeft+(r*****s):l>0&&(d=t.scrollLeft-(r-1.8*s)))};t.addEventListener("mousemove",a,{passive:!0}),document.addEventListener("mouseup",()=>{t.removeEventListener("mousemove",a),t.style.cursor=null,d<0&&(d=0),t.scroll({left:d,behavior:"smooth"}),this.draggingtimer=setTimeout(()=>{s.classList.remove("dragging")},550)},{once:!0,passive:!0})}};window.swiffyslider.extensions=swiffysliderextensions,document.currentScript.hasAttribute("data-noinit")||window.addEventListener("load",()=>{swiffyslider.extensions.init()});
//# sourceMappingURL=swiffy-slider-extensions.min.js.map
