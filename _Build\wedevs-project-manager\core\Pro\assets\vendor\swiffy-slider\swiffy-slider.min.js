/*!
 * swiffy-slider version: "1.6.0",
 * https://github.com/dynamicweb/swiffy-slide
 * Licensed MIT
 * Copyright (c) 2021 Dynamicweb Software A/S
 */
const swiffyslider={version:"1.6.0",init(e=document.body){e.querySelectorAll(".swiffy-slider").forEach(e=>this.initSlider(e))},initSlider(e){if(e.querySelectorAll(".slider-nav").forEach(t=>t.addEventListener("click",()=>this.slide(e,t.classList.contains("slider-nav-next")),{passive:!0})),e.querySelectorAll(".slider-indicators").forEach(t=>{t.addEventListener("click",()=>this.slideToByIndicator()),this.onSlideEnd(e,()=>this.handleIndicators(e),60)}),e.classList.contains("slider-nav-autoplay")){const t=e.getAttribute("data-slider-nav-autoplay-interval")?e.getAttribute("data-slider-nav-autoplay-interval"):2500;this.autoPlay(e,t,e.classList.contains("slider-nav-autopause"))}if(["slider-nav-autohide","slider-nav-animation"].some(t=>e.classList.contains(t))){const t=e.getAttribute("data-slider-nav-animation-threshold")?e.getAttribute("data-slider-nav-animation-threshold"):.3;this.setVisibleSlides(e,t)}},setVisibleSlides(e,t=.3){let i=new IntersectionObserver(t=>{t.forEach(e=>{e.isIntersecting?e.target.classList.add("slide-visible"):e.target.classList.remove("slide-visible")}),e.querySelector(".slider-container>*:first-child").classList.contains("slide-visible")?e.classList.add("slider-item-first-visible"):e.classList.remove("slider-item-first-visible"),e.querySelector(".slider-container>*:last-child").classList.contains("slide-visible")?e.classList.add("slider-item-last-visible"):e.classList.remove("slider-item-last-visible")},{root:e.querySelector(".slider-container"),threshold:t});e.querySelectorAll(".slider-container>*").forEach(e=>i.observe(e))},slide(e,t=!0){const i=e.querySelector(".slider-container"),s=e.classList.contains("slider-nav-page"),l=e.classList.contains("slider-nav-noloop"),r=e.classList.contains("slider-nav-nodelay"),a=i.children,o=parseInt(window.getComputedStyle(i).columnGap),n=a[0].offsetWidth+o;let d=t?i.scrollLeft+n:i.scrollLeft-n;s&&(d=t?i.scrollLeft+i.offsetWidth:i.scrollLeft-i.offsetWidth),i.scrollLeft<1&&!t&&!l&&(d=i.scrollWidth-i.offsetWidth),i.scrollLeft>=i.scrollWidth-i.offsetWidth&&t&&!l&&(d=0),i.scroll({left:d,behavior:r?"auto":"smooth"})},slideToByIndicator(){const e=window.event.target,t=Array.from(e.parentElement.children).indexOf(e),i=e.parentElement.children.length,s=e.closest(".swiffy-slider"),l=s.querySelector(".slider-container").children.length/i*t;this.slideTo(s,l)},slideTo(e,t){const i=e.querySelector(".slider-container"),s=parseInt(window.getComputedStyle(i).columnGap),l=i.children[0].offsetWidth+s,r=e.classList.contains("slider-nav-nodelay");i.scroll({left:l*t,behavior:r?"auto":"smooth"})},onSlideEnd(e,t,i=125){let s;e.querySelector(".slider-container").addEventListener("scroll",()=>{window.clearTimeout(s),s=setTimeout(t,i)},{capture:!1,passive:!0})},autoPlay(e,t,i){t=t<750?750:t;let s=setInterval(()=>this.slide(e),t);const l=()=>this.autoPlay(e,t,i);return i&&(["mouseover","touchstart"].forEach(t=>{e.addEventListener(t,()=>{window.clearTimeout(s)},{once:!0,passive:!0})}),["mouseout","touchend"].forEach(t=>{e.addEventListener(t,()=>{l()},{once:!0,passive:!0})})),s},handleIndicators(e){if(!e)return;const t=e.querySelector(".slider-container"),i=t.scrollWidth-t.offsetWidth,s=t.scrollLeft/i;e.querySelectorAll(".slider-indicators").forEach(e=>{let t=e.children,i=Math.abs(Math.round((t.length-1)*s));for(let e of t)e.classList.remove("active");t[i].classList.add("active")})}};window.swiffyslider=swiffyslider,document.currentScript.hasAttribute("data-noinit")||(document.currentScript.hasAttribute("defer")?swiffyslider.init():document.onreadystatechange=()=>{"interactive"===document.readyState&&swiffyslider.init()});
//# sourceMappingURL=swiffy-slider.min.js.map
